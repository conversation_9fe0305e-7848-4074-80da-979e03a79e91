/**
 * <PERSON><PERSON><PERSON><PERSON> RPG - BUNDLED VERSION FOR HOSTING COMPATIBILITY
 * All modules combined into a single file for easy deployment
 */

// ===== CONFIGURATION =====
const CONFIG = {
    game: {
        title: "Agartha: The Lost City of Light",
        version: "2.0.0",
        maxPlayers: 12,
        maxMessages: 150,
        saveInterval: 45000,
        autoSaveEnabled: true,
        debugMode: false
    },
    
    // AI Model configurations for WebLLM + Hugging Face
    // Full range from tiny to powerful models
    models: {
        'demo-mode': {
            name: 'Demo Mode',
            displayName: '🎭 Demo Mode (Always Works)',
            description: 'Instant testing with pre-written responses - no download required, guaranteed compatibility!',
            contextLength: 4096,
            temperature: 0.8,
            topP: 0.9,
            maxTokens: 250,
            size: '0MB',
            params: 'Demo',
            speed: 'Instant',
            isDemo: true,
            recommended: true,
            huggingFaceRepo: 'Built-in demo responses'
        },
        // === TEST THESE MODELS (Real WebLLM IDs) ===
        'TinyLlama-1.1B-Chat-v1.0-q4f16_1-MLC': {
            name: 'TinyLlama 1.1B',
            displayName: '🐭 TinyLlama 1.1B (Test Tiny)',
            description: 'Ultra-small model - try this first! If it works, perfect for bundling.',
            contextLength: 2048,
            temperature: 0.8,
            topP: 0.9,
            maxTokens: 200,
            size: '637MB',
            params: '1.1B',
            speed: 'Very Fast',
            huggingFaceRepo: 'TinyLlama/TinyLlama-1.1B-Chat-v1.0'
        },
        'Qwen2-0.5B-Instruct-q4f16_1-MLC': {
            name: 'Qwen2 0.5B',
            displayName: '⚡ Qwen2 0.5B (Test Super Tiny)',
            description: 'Even smaller! If this works, it\'s the best choice for bundling.',
            contextLength: 2048,
            temperature: 0.8,
            topP: 0.9,
            maxTokens: 150,
            size: '300MB',
            params: '0.5B',
            speed: 'Lightning',
            huggingFaceRepo: 'Qwen/Qwen2-0.5B-Instruct'
        },
        'Phi-3-mini-4k-instruct-q4f16_1-MLC': {
            name: 'Phi-3 Mini',
            displayName: '⭐ Phi-3 Mini (Test Regular)',
            description: 'Good quality model - test if you want better responses than tiny models.',
            contextLength: 4096,
            temperature: 0.8,
            topP: 0.95,
            maxTokens: 300,
            size: '2.2GB',
            params: '3.8B',
            speed: 'Good',
            huggingFaceRepo: 'microsoft/Phi-3-mini-4k-instruct'
        },
        'gemma-2-2b-it-q4f16_1-MLC': {
            name: 'Gemma 2 2B',
            displayName: '💎 Gemma 2 2B (Test Creative)',
            description: 'Creative model - test for storytelling quality comparison.',
            contextLength: 8192,
            temperature: 0.85,
            topP: 0.9,
            maxTokens: 350,
            size: '1.6GB',
            params: '2B',
            speed: 'Good',
            huggingFaceRepo: 'google/gemma-2-2b-it'
        },
        'Qwen2-7B-Instruct-q4f16_1-MLC': {
            name: 'Qwen2 7B',
            displayName: '🌟 Qwen2 7B (Advanced)',
            description: 'Alibaba\'s powerful model with excellent creative writing abilities',
            contextLength: 8192,
            temperature: 0.85,
            topP: 0.9,
            maxTokens: 500,
            size: '4.2GB',
            params: '7B',
            speed: 'Slower',
            huggingFaceRepo: 'Qwen/Qwen2-7B-Instruct'
        },
        'demo-mode': {
            name: 'Demo Mode',
            displayName: '🎭 Demo Mode (No Download)',
            description: 'Test the game with pre-written responses - no AI model download required',
            contextLength: 4096,
            temperature: 0.8,
            topP: 0.9,
            maxTokens: 250,
            size: '0MB',
            params: 'Demo',
            speed: 'Instant',
            isDemo: true,
            huggingFaceRepo: 'Built-in demo responses'
        }
    },
    
    characterClasses: {
        'Crystal Keeper': {
            name: 'Crystal Keeper',
            title: 'Master of Ancient Energies',
            description: 'Harnesses the power of crystalline matrices and sacred geometries',
            primaryStat: 'vril',
            baseStats: { vril: 18, wisdom: 16, resonance: 15, vitality: 12, agility: 10, strength: 9 },
            startingItems: ['Crystal Focus', 'Energy Amplifier', 'Meditation Beads'],
            abilities: ['Crystal Resonance', 'Energy Channeling', 'Harmonic Healing'],
            lore: 'Descendants of the first Agarthans who learned to commune with the living crystals that power the inner realm.'
        },
        'Vril Engineer': {
            name: 'Vril Engineer',
            title: 'Architect of Energy',
            description: 'Masters the technological applications of Vril energy',
            primaryStat: 'technology',
            baseStats: { technology: 18, vril: 15, intelligence: 16, vitality: 11, agility: 10, strength: 10 },
            startingItems: ['Vril Conductor', 'Energy Scanner', 'Technical Manual'],
            abilities: ['Energy Manipulation', 'Tech Interface', 'System Override'],
            lore: 'Brilliant minds who bridge ancient Vril wisdom with cutting-edge technology to maintain Agartha\'s infrastructure.'
        },
        'Lemurian Scholar': {
            name: 'Lemurian Scholar',
            title: 'Keeper of Ancient Wisdom',
            description: 'Preserves and interprets the knowledge of lost civilizations',
            primaryStat: 'wisdom',
            baseStats: { wisdom: 18, intelligence: 16, telepathy: 15, vitality: 10, agility: 9, strength: 8 },
            startingItems: ['Ancient Codex', 'Memory Crystal', 'Scholar\'s Robes'],
            abilities: ['Ancient Knowledge', 'Telepathic Link', 'Lore Mastery'],
            lore: 'Guardians of Lemurian traditions who can access the collective memory of their ancestors through meditation and crystal communion.'
        },
        'Atlantean Warrior': {
            name: 'Atlantean Warrior',
            title: 'Guardian of the Depths',
            description: 'Elite fighter trained in both physical and energy combat',
            primaryStat: 'strength',
            baseStats: { strength: 18, vitality: 16, agility: 15, vril: 12, intelligence: 10, wisdom: 9 },
            startingItems: ['Crystal Blade', 'Energy Shield', 'Warrior\'s Armor'],
            abilities: ['Combat Mastery', 'Energy Strike', 'Defensive Stance'],
            lore: 'Descendants of Atlantis\'s greatest warriors, trained in combat techniques that blend physical prowess with energy manipulation.'
        },
        'Inner Earth Scout': {
            name: 'Inner Earth Scout',
            title: 'Explorer of Hidden Realms',
            description: 'Expert navigator of Agartha\'s vast tunnel networks',
            primaryStat: 'agility',
            baseStats: { agility: 18, perception: 16, vitality: 15, intelligence: 12, strength: 10, wisdom: 9 },
            startingItems: ['Navigation Tools', 'Climbing Gear', 'Emergency Supplies'],
            abilities: ['Pathfinding', 'Stealth Movement', 'Danger Sense'],
            lore: 'Brave explorers who map the ever-changing passages of the inner Earth and serve as guides for travelers between realms.'
        },
        'Light Weaver': {
            name: 'Light Weaver',
            title: 'Manipulator of Consciousness',
            description: 'Channels the power of pure consciousness and light',
            primaryStat: 'telepathy',
            baseStats: { telepathy: 18, wisdom: 16, vril: 15, intelligence: 12, vitality: 10, agility: 9 },
            startingItems: ['Light Crystal', 'Consciousness Amplifier', 'Meditation Focus'],
            abilities: ['Light Manipulation', 'Mind Bridge', 'Consciousness Expansion'],
            lore: 'Rare individuals who can directly manipulate the fundamental forces of consciousness and light that permeate Agartha.'
        }
    }
};

// ===== UTILITY FUNCTIONS =====
function getModelConfig(modelId) {
    return CONFIG.models[modelId] || null;
}

function getCharacterClass(className) {
    return CONFIG.characterClasses[className] || null;
}

// ===== DEMO RESPONSES =====
const DEMO_RESPONSES = [
    "As you step through the Crystal Gates, the air shimmers with ancient energy. The Guardian Zephyr nods approvingly as you pass, and you find yourself in a vast underground plaza filled with beings from across the inner realms. Crystalline spires reach toward a glowing central sun, and the sound of flowing water echoes from hidden fountains. What draws your attention first?",

    "The mystical energies of Agartha respond to your presence. You notice a group of robed figures near an ornate fountain discussing something in hushed tones. To your left, a merchant's stall displays glowing crystals and ancient artifacts. Ahead, a grand staircase leads to what appears to be a library or hall of records. The very air seems alive with possibility.",

    "Your actions resonate through the crystalline structures around you. The ambient light shifts slightly, as if the city itself is acknowledging your presence. A gentle breeze carries the scent of exotic flowers and the faint sound of distant chanting. You feel a growing connection to the ancient wisdom that permeates this sacred place.",

    "The inhabitants of Agartha watch you with curious but welcoming eyes. Some appear human-like but with an otherworldly grace, while others seem to be beings of pure energy taking temporary form. A young woman with silver hair approaches and speaks in a melodious voice: 'Welcome, surface dweller. The Council of Elders has been expecting someone like you.'",

    "As you explore deeper into the realm, you discover that Agartha is far more vast than you initially imagined. Tunnels branch off in all directions, leading to different districts and settlements. The architecture seamlessly blends natural crystal formations with advanced technology that seems to run on pure consciousness and intention.",

    "Your journey takes an unexpected turn as you encounter ancient murals depicting the history of the three great civilizations: Lemuria, Atlantis, and Agartha itself. The images seem to move and shift as you watch, telling stories of great achievements, tragic falls, and the eternal hope for unity between the surface and inner worlds.",

    "The energy of the place fills you with a sense of purpose and destiny. You realize that your arrival here was no accident - you have been called to play a role in the great awakening that is to come. The question is: what path will you choose to walk in this mystical realm?"
];

// ===== AI MANAGER =====
class AIManager {
    constructor() {
        this.engine = null;
        this.currentModel = null;
        this.isReady = false;
        this.conversationHistory = [];
        this.demoResponseIndex = 0;
        this.isDemoMode = false;
    }

    async verifyModelAccess() {
        try {
            console.log('🔍 Verifying WebLLM model access...');

            // Test basic connectivity first
            console.log('🌐 Testing CDN connectivity...');
            const response = await fetch('https://esm.run/@mlc-ai/web-llm', { method: 'HEAD' });
            if (!response.ok) {
                throw new Error(`CDN not accessible: ${response.status}`);
            }
            console.log('✅ CDN is accessible');

            const webllm = await import("https://esm.run/@mlc-ai/web-llm");
            console.log('✅ WebLLM imported successfully');

            if (webllm.prebuiltAppConfig && webllm.prebuiltAppConfig.model_list) {
                const availableModels = webllm.prebuiltAppConfig.model_list;
                console.log('📋 Available WebLLM models:', availableModels.map(m => m.model_id));

                const configuredModels = Object.keys(CONFIG.models).filter(id => id !== 'demo-mode');
                const missingModels = configuredModels.filter(modelId =>
                    !availableModels.some(m => m.model_id === modelId)
                );

                if (missingModels.length > 0) {
                    console.warn('⚠️ Some configured models are not in WebLLM prebuilt list:', missingModels);
                    console.log('💡 Consider using Demo Mode for testing');
                } else {
                    console.log('✅ All configured models are available in WebLLM');
                }

                return { availableModels, configuredModels, missingModels };
            } else {
                console.warn('⚠️ WebLLM prebuilt config not found');
                return null;
            }

        } catch (error) {
            console.error('❌ Failed to verify model access:', error);
            console.log('💡 WebLLM may not be accessible. Demo Mode is still available.');
            return null;
        }
    }

    async initialize(modelId, options = {}) {
        try {
            console.log(`🤖 Initializing AI with model: ${modelId}`);

            const modelConfig = getModelConfig(modelId);
            if (!modelConfig) {
                throw new Error(`Unknown model: ${modelId}`);
            }

            // Check if this is demo mode
            if (modelConfig.isDemo) {
                console.log('🎭 Demo Mode activated - no download required!');
                this.isDemoMode = true;
                this.currentModel = modelId;
                this.isReady = true;

                // Simulate loading progress for demo
                if (options.onProgress) {
                    const steps = [
                        { progress: 0.2, text: 'Initializing demo mode...' },
                        { progress: 0.5, text: 'Loading demo responses...' },
                        { progress: 0.8, text: 'Preparing game environment...' },
                        { progress: 1.0, text: 'Demo mode ready!' }
                    ];

                    for (const step of steps) {
                        await new Promise(resolve => setTimeout(resolve, 300));
                        options.onProgress(step);
                    }
                }

                console.log('✅ Demo mode initialized successfully');
                return true;
            }

            // Regular WebLLM initialization
            console.log(`📦 This will download from Hugging Face: mlc-ai/${modelId}`);

            try {
                console.log('📥 Importing WebLLM from CDN...');
                const webllm = await import("https://esm.run/@mlc-ai/web-llm");
                console.log('✅ WebLLM imported successfully');
                console.log('📋 Available functions:', Object.keys(webllm));

                console.log(`🔄 Creating MLC Engine for ${modelId}...`);
                console.log(`📍 Model will be downloaded from: ${modelConfig.huggingFaceRepo}`);

                // Check if the model exists in WebLLM's prebuilt list
                if (webllm.prebuiltAppConfig && webllm.prebuiltAppConfig.model_list) {
                    const availableModels = webllm.prebuiltAppConfig.model_list.map(m => m.model_id);
                    console.log('📋 Total available WebLLM models:', availableModels.length);
                    console.log('📋 First 20 available models:', availableModels.slice(0, 20));

                    // Check our specific test models
                    const ourTestModels = [
                        'TinyLlama-1.1B-Chat-v1.0-q4f16_1-MLC',
                        'Qwen2-0.5B-Instruct-q4f16_1-MLC',
                        'Phi-3-mini-4k-instruct-q4f16_1-MLC',
                        'gemma-2-2b-it-q4f16_1-MLC'
                    ];

                    console.log('🎯 TESTING OUR MODELS:');
                    ourTestModels.forEach(modelId => {
                        const exists = availableModels.includes(modelId);
                        console.log(`${exists ? '✅' : '❌'} ${modelId} - ${exists ? 'AVAILABLE' : 'NOT FOUND'}`);
                    });

                    // Look for similar models in case exact names don't match
                    const similarModels = availableModels.filter(id =>
                        id.toLowerCase().includes('tinyllama') ||
                        id.toLowerCase().includes('phi') ||
                        id.toLowerCase().includes('gemma') ||
                        id.toLowerCase().includes('qwen')
                    );
                    console.log('🔍 Similar models found:', similarModels.slice(0, 10));

                    // Show some working model examples
                    console.log('💡 To update config, use model IDs from the lists above');
                    console.log('💡 Common working patterns: *-q4f16_1-MLC, *-q4f32_1-MLC');

                    if (!availableModels.includes(modelId)) {
                        console.warn(`⚠️ Model ${modelId} not found in prebuilt list`);
                        console.log('🔍 Trying anyway...');
                    } else {
                        console.log(`✅ Model ${modelId} found in prebuilt list`);
                    }
                } else {
                    console.warn('⚠️ WebLLM prebuilt config not available');
                }

                this.engine = await webllm.CreateMLCEngine(modelId, {
                    initProgressCallback: (progress) => {
                        console.log(`📊 AI Loading Progress:`, progress);
                        if (options.onProgress) {
                            options.onProgress(progress);
                        }
                    }
                });

            } catch (importError) {
                console.error('❌ WebLLM import failed:', importError);
                throw new Error(`Failed to import WebLLM: ${importError.message}`);
            }

            this.currentModel = modelId;
            this.isReady = true;

            console.log(`✅ AI engine initialized successfully with ${modelConfig.name}`);
            console.log(`🎯 Model loaded from Hugging Face: ${modelConfig.huggingFaceRepo}`);
            return true;

        } catch (error) {
            console.error('❌ Failed to initialize AI engine:', error);
            console.error('🔍 Error details:', error.message);
            console.error('💡 Check if model ID is correct and Hugging Face is accessible');

            // Offer fallback to demo mode
            console.log('🎭 Offering demo mode as fallback...');
            const useDemo = confirm('AI model failed to load. Would you like to use Demo Mode instead? (Click OK for Demo Mode, Cancel to retry)');

            if (useDemo) {
                console.log('🎭 Switching to demo mode...');
                this.isDemoMode = true;
                this.currentModel = 'demo-mode';
                this.isReady = true;

                if (options.onProgress) {
                    options.onProgress({ progress: 1.0, text: 'Demo mode activated!' });
                }

                return true;
            } else {
                this.isReady = false;
                throw error;
            }
        }
    }

    async generateResponse(prompt, context = {}) {
        if (!this.isReady) {
            throw new Error('AI engine not initialized');
        }

        try {
            // Handle demo mode
            if (this.isDemoMode) {
                console.log('🎭 Generating demo response...');

                // Add a small delay to simulate thinking
                await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1200));

                // Get next demo response
                const response = DEMO_RESPONSES[this.demoResponseIndex % DEMO_RESPONSES.length];
                this.demoResponseIndex++;

                // Add some variation based on user input
                if (prompt.toLowerCase().includes('look') || prompt.toLowerCase().includes('examine')) {
                    return `You carefully observe your surroundings. ${response}`;
                } else if (prompt.toLowerCase().includes('talk') || prompt.toLowerCase().includes('speak')) {
                    return `Your words echo through the crystalline halls. ${response}`;
                } else if (prompt.toLowerCase().includes('go') || prompt.toLowerCase().includes('move')) {
                    return `You move forward with purpose. ${response}`;
                } else {
                    return response;
                }
            }

            // Regular WebLLM response
            if (!this.engine) {
                throw new Error('WebLLM engine not initialized');
            }

            const response = await this.engine.chat.completions.create({
                messages: [{ role: "user", content: prompt }],
                temperature: context.temperature || 0.8,
                max_tokens: context.maxTokens || 250
            });

            return response.choices[0].message.content;
        } catch (error) {
            console.error('❌ Failed to generate AI response:', error);
            throw error;
        }
    }
}

// ===== MAIN APPLICATION =====
class AgarthaApp {
    constructor() {
        this.aiManager = new AIManager();
        this.selectedModel = null;
        this.gameState = {
            player: null,
            currentLocation: 'Crystal Gates of Shambhala',
            inventory: [],
            experience: 0,
            level: 1
        };
    }

    async initialize() {
        console.log('🚀 Agartha RPG initializing...');
        
        try {
            // Verify model access
            await this.aiManager.verifyModelAccess();
            
            // Populate model options
            this.populateModelOptions();
            
            // Set up event listeners
            this.setupEventListeners();
            
            console.log('✅ Agartha RPG initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Agartha RPG:', error);
        }
    }

    populateModelOptions() {
        console.log('🎯 Populating model options...');
        
        const container = document.getElementById('modelOptions');
        if (!container) {
            console.error('❌ modelOptions container not found');
            return;
        }

        container.innerHTML = '';

        Object.entries(CONFIG.models).forEach(([modelId, config]) => {
            const option = document.createElement('div');
            option.className = `model-option hover-lift ${config.isDemo ? 'demo-option' : ''}`;
            option.dataset.model = modelId;

            const demoStyle = config.isDemo ? 'style="border: 2px solid #ffd700; background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));"' : '';

            option.innerHTML = `
                <div class="model-header" ${demoStyle}>
                    <h3>${config.displayName}</h3>
                    <div class="model-specs">
                        <span class="badge ${config.isDemo ? 'badge-warning' : 'badge-primary'}">${config.size}</span>
                        <span class="badge badge-secondary">${config.speed}</span>
                        ${config.isDemo ? '<span class="badge badge-success">No Download</span>' : ''}
                    </div>
                </div>
                <p class="model-description">${config.description}</p>
                <div class="model-stats">
                    <div class="stat">
                        <span class="stat-label">Parameters:</span>
                        <span class="stat-value">${config.params}</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Context:</span>
                        <span class="stat-value">${config.contextLength}</span>
                    </div>
                    ${config.isDemo ? '<div class="stat"><span class="stat-label">Perfect for:</span><span class="stat-value">Testing & Demos</span></div>' : ''}
                </div>
            `;

            option.addEventListener('click', () => this.selectModel(modelId, option));
            container.appendChild(option);
        });
        
        console.log('✅ Model options populated');
    }

    selectModel(modelId, optionElement) {
        // Remove previous selections
        document.querySelectorAll('.model-option').forEach(opt => 
            opt.classList.remove('selected'));
        
        // Select this option
        optionElement.classList.add('selected');
        this.selectedModel = modelId;
        
        // Enable start button
        const startBtn = document.getElementById('startBtn');
        if (startBtn) {
            startBtn.disabled = false;
        }
        
        console.log(`🎯 Model selected: ${modelId}`);
    }

    setupEventListeners() {
        const startBtn = document.getElementById('startBtn');
        if (startBtn) {
            startBtn.addEventListener('click', () => this.initializeAI());
        }

        const createCharacterBtn = document.getElementById('createCharacterBtn');
        if (createCharacterBtn) {
            createCharacterBtn.addEventListener('click', () => this.createCharacter());
        }
    }

    async initializeAI() {
        if (!this.selectedModel) {
            alert('Please select an AI model first');
            return;
        }

        const startBtn = document.getElementById('startBtn');
        const statusText = document.getElementById('statusText');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');

        try {
            startBtn.disabled = true;
            startBtn.innerHTML = '<span>🔄 Initializing AI...</span>';
            statusText.textContent = 'Downloading AI model from Hugging Face...';
            progressBar.style.display = 'block';

            await this.aiManager.initialize(this.selectedModel, {
                onProgress: (progress) => {
                    if (progress.progress !== undefined) {
                        const percent = Math.round(progress.progress * 100);
                        progressFill.style.width = `${percent}%`;
                        progressFill.textContent = `${percent}%`;
                        statusText.textContent = progress.text || `Loading... ${percent}%`;
                    }
                }
            });

            // Hide model setup and show character creation
            document.getElementById('modelSetup').classList.add('hidden');
            const charCreation = document.getElementById('characterCreation');
            charCreation.classList.remove('hidden');
            charCreation.classList.add('active');
            this.populateCharacterClasses();

        } catch (error) {
            console.error('❌ AI initialization failed:', error);
            startBtn.disabled = false;
            startBtn.innerHTML = '<span>🧙‍♂️ Initialize AI Dungeon Master</span>';
            statusText.textContent = 'Failed to initialize AI. Please try again.';
            progressBar.style.display = 'none';
            alert('Failed to initialize AI model. Please check your internet connection and try again.');
        }
    }

    populateCharacterClasses() {
        console.log('🎭 Populating character classes...');
        const container = document.getElementById('characterClasses');
        if (!container) {
            console.error('❌ characterClasses container not found');
            return;
        }
        console.log('✅ characterClasses container found');

        container.innerHTML = '';
        console.log('📋 Available character classes:', Object.keys(CONFIG.characterClasses));

        Object.entries(CONFIG.characterClasses).forEach(([className, classData]) => {
            const classOption = document.createElement('div');
            classOption.className = 'character-class hover-lift';
            classOption.dataset.class = className;
            classOption.innerHTML = `
                <h3>${classData.name}</h3>
                <h4>${classData.title}</h4>
                <p>${classData.description}</p>
                <div class="class-stats">
                    <div class="stat-grid">
                        ${Object.entries(classData.baseStats).map(([stat, value]) => 
                            `<div class="stat"><span>${stat}:</span><span>${value}</span></div>`
                        ).join('')}
                    </div>
                </div>
            `;
            
            classOption.addEventListener('click', () => {
                document.querySelectorAll('.character-class').forEach(opt => 
                    opt.classList.remove('selected'));
                classOption.classList.add('selected');
                
                const createBtn = document.getElementById('createCharacterBtn');
                if (createBtn) createBtn.disabled = false;
            });
            
            container.appendChild(classOption);
        });

        console.log('✅ Character classes populated successfully');
    }

    createCharacter() {
        const selectedClass = document.querySelector('.character-class.selected');
        if (!selectedClass) {
            alert('Please select a character class');
            return;
        }

        const playerName = document.getElementById('playerName').value.trim();
        if (!playerName) {
            alert('Please enter a character name');
            return;
        }

        const className = selectedClass.dataset.class;
        const classData = CONFIG.characterClasses[className];

        this.gameState.player = {
            name: playerName,
            class: className,
            classData: classData,
            stats: { ...classData.baseStats },
            inventory: [...classData.startingItems],
            abilities: [...classData.abilities],
            level: 1,
            experience: 0
        };

        console.log('✅ Character created:', this.gameState.player);

        // Save player data and AI model to localStorage
        localStorage.setItem('agarthaPlayer', JSON.stringify(this.gameState.player));
        localStorage.setItem('agarthaAIModel', this.selectedModel);

        // Redirect to game page
        console.log('🚀 Redirecting to game page...');
        window.location.href = 'game.html';
    }

    startGame() {
        console.log('🎮 Starting Agartha adventure...');

        // Initialize game UI
        this.updatePlayerInfo();
        this.updateInventory();
        this.showWelcomeMessage();
    }

    updatePlayerInfo() {
        const playerInfo = document.getElementById('playerInfo');
        if (playerInfo && this.gameState.player) {
            playerInfo.innerHTML = `
                <div class="player-card">
                    <h3>${this.gameState.player.name}</h3>
                    <p class="player-class">${this.gameState.player.classData.title}</p>
                    <div class="player-stats">
                        <div class="stat">Level: ${this.gameState.player.level}</div>
                        <div class="stat">XP: ${this.gameState.player.experience}</div>
                    </div>
                </div>
            `;
        }
    }

    updateInventory() {
        const inventoryList = document.getElementById('inventoryList');
        if (inventoryList && this.gameState.player) {
            inventoryList.innerHTML = this.gameState.player.inventory
                .map(item => `<div class="inventory-item">${item}</div>`)
                .join('');
        }
    }

    showWelcomeMessage() {
        const gameOutput = document.getElementById('gameOutput');
        if (gameOutput) {
            const demoNotice = this.aiManager.isDemoMode ?
                '<div class="badge badge-warning mb-md">🎭 Demo Mode Active - Using pre-written responses</div>' : '';

            const welcomeMessage = `
                <div class="message ai-message">
                    <div class="message-content">
                        ${demoNotice}
                        <h3>🌟 Welcome to Agartha, ${this.gameState.player.name}!</h3>
                        <p>As a ${this.gameState.player.classData.name}, you stand before the magnificent Crystal Gates of Shambhala. The ancient portals shimmer with otherworldly energy, and you can feel the power of the inner realm calling to you.</p>
                        <p>${this.gameState.player.classData.lore}</p>
                        <p>Your journey into the mystical underground realm begins now. What would you like to do?</p>
                        ${this.aiManager.isDemoMode ? '<p><em>Try commands like: "look around", "talk to the guardian", "examine the gates", or "go to the plaza"</em></p>' : ''}
                    </div>
                </div>
            `;
            gameOutput.innerHTML = welcomeMessage;
        }

        // Enable game input
        const gameInput = document.getElementById('gameInput');
        const sendBtn = document.getElementById('sendBtn');
        if (gameInput && sendBtn) {
            gameInput.disabled = false;
            sendBtn.disabled = false;
            gameInput.focus();

            gameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            sendBtn.addEventListener('click', () => this.sendMessage());
        }
    }

    async sendMessage() {
        const gameInput = document.getElementById('gameInput');
        const gameOutput = document.getElementById('gameOutput');

        if (!gameInput || !gameOutput) return;

        const message = gameInput.value.trim();
        if (!message) return;

        // Add player message to output
        const playerMessage = `
            <div class="message player-message">
                <div class="message-content">${message}</div>
            </div>
        `;
        gameOutput.innerHTML += playerMessage;
        gameInput.value = '';

        // Scroll to bottom
        gameOutput.scrollTop = gameOutput.scrollHeight;

        try {
            // Generate AI response
            const context = {
                player: this.gameState.player,
                location: this.gameState.currentLocation,
                inventory: this.gameState.player.inventory
            };

            const prompt = `You are the AI Dungeon Master for Agartha RPG. The player is ${this.gameState.player.name}, a ${this.gameState.player.class} currently at ${this.gameState.currentLocation}.

Player action: "${message}"

Respond as the dungeon master, describing what happens next in this mystical underground realm. Keep responses engaging, immersive, and around 2-3 paragraphs.`;

            const aiResponse = await this.aiManager.generateResponse(prompt, context);

            // Add AI response to output
            const aiMessage = `
                <div class="message ai-message">
                    <div class="message-content">${aiResponse}</div>
                </div>
            `;
            gameOutput.innerHTML += aiMessage;

        } catch (error) {
            console.error('❌ Failed to generate AI response:', error);
            const errorMessage = `
                <div class="message ai-message error">
                    <div class="message-content">
                        <em>The mystical energies seem disrupted... Please try again.</em>
                    </div>
                </div>
            `;
            gameOutput.innerHTML += errorMessage;
        }

        // Scroll to bottom
        gameOutput.scrollTop = gameOutput.scrollHeight;
    }

    // Initialize game page with stored player data
    initializeGamePage(playerData, aiModel) {
        console.log('🎮 Initializing game page with player:', playerData);

        // Restore game state
        this.gameState.player = playerData;
        this.selectedModel = aiModel;

        // Initialize AI if not already done
        if (!this.aiManager.isReady) {
            this.aiManager.initialize(aiModel).then(() => {
                this.startGamePage();
            }).catch(error => {
                console.error('❌ Failed to initialize AI on game page:', error);
                // Continue with demo mode or show error
                this.startGamePage();
            });
        } else {
            this.startGamePage();
        }
    }

    startGamePage() {
        console.log('🌟 Starting game page...');

        // Update player info in sidebar
        this.updateGamePagePlayerInfo();
        this.updateGamePageInventory();
        this.showGamePageWelcome();

        // Enable input
        const gameInput = document.getElementById('gameInput');
        const sendBtn = document.getElementById('sendBtn');

        if (gameInput && sendBtn) {
            gameInput.disabled = false;
            sendBtn.disabled = false;
            gameInput.focus();

            // Add event listeners
            gameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendGameMessage();
                }
            });

            sendBtn.addEventListener('click', () => this.sendGameMessage());
        }
    }

    updateGamePagePlayerInfo() {
        const playerName = document.getElementById('playerName');
        const playerClass = document.getElementById('playerClass');
        const playerStats = document.getElementById('playerStats');

        if (playerName && this.gameState.player) {
            playerName.textContent = this.gameState.player.name;
        }

        if (playerClass && this.gameState.player) {
            playerClass.textContent = this.gameState.player.classData.title;
        }

        if (playerStats && this.gameState.player) {
            playerStats.innerHTML = Object.entries(this.gameState.player.stats)
                .map(([stat, value]) => `
                    <div class="stat">
                        <span>${stat}:</span>
                        <span>${value}</span>
                    </div>
                `).join('');
        }
    }

    updateGamePageInventory() {
        const inventoryList = document.getElementById('inventoryList');
        if (inventoryList && this.gameState.player) {
            inventoryList.innerHTML = this.gameState.player.inventory
                .map(item => `<div class="inventory-item">${item}</div>`)
                .join('');
        }
    }

    showGamePageWelcome() {
        const gameOutput = document.getElementById('gameOutput');
        if (gameOutput && this.gameState.player) {
            const demoNotice = this.aiManager.isDemoMode ?
                '<div class="badge badge-warning" style="margin-bottom: 10px;">🎭 Demo Mode Active</div>' : '';

            const welcomeMessage = `
                <div class="message ai">
                    ${demoNotice}
                    <h3>🌟 Welcome to Agartha, ${this.gameState.player.name}!</h3>
                    <p>As a ${this.gameState.player.classData.name}, you stand before the magnificent Crystal Gates of Shambhala. The ancient portals shimmer with otherworldly energy, and you can feel the power of the inner realm calling to you.</p>
                    <p>${this.gameState.player.classData.lore}</p>
                    <p>Your journey into the mystical underground realm begins now. What would you like to do?</p>
                    ${this.aiManager.isDemoMode ? '<p><em>Try: "look around", "examine the gates", "talk to the guardian"</em></p>' : ''}
                </div>
            `;
            gameOutput.innerHTML = welcomeMessage;
        }
    }

    sendGameMessage() {
        const gameInput = document.getElementById('gameInput');
        const gameOutput = document.getElementById('gameOutput');

        if (!gameInput || !gameOutput) return;

        const message = gameInput.value.trim();
        if (!message) return;

        // Add player message
        const playerMessage = document.createElement('div');
        playerMessage.className = 'message player';
        playerMessage.textContent = message;
        gameOutput.appendChild(playerMessage);

        gameInput.value = '';
        gameOutput.scrollTop = gameOutput.scrollHeight;

        // Generate AI response (reuse existing method)
        this.generateAIResponse(message, gameOutput);
    }

    async generateAIResponse(message, gameOutput) {
        try {
            const context = {
                player: this.gameState.player,
                location: this.gameState.currentLocation,
                inventory: this.gameState.player.inventory
            };

            const prompt = `You are the AI Dungeon Master for Agartha RPG. The player is ${this.gameState.player.name}, a ${this.gameState.player.class} currently at ${this.gameState.currentLocation}.

Player action: "${message}"

Respond as the dungeon master, describing what happens next in this mystical underground realm. Keep responses engaging, immersive, and around 2-3 paragraphs.`;

            const aiResponse = await this.aiManager.generateResponse(prompt, context);

            // Add AI response
            const aiMessage = document.createElement('div');
            aiMessage.className = 'message ai';
            aiMessage.innerHTML = aiResponse;
            gameOutput.appendChild(aiMessage);

        } catch (error) {
            console.error('❌ Failed to generate AI response:', error);
            const errorMessage = document.createElement('div');
            errorMessage.className = 'message ai';
            errorMessage.innerHTML = '<em>The mystical energies seem disrupted... Please try again.</em>';
            gameOutput.appendChild(errorMessage);
        }

        gameOutput.scrollTop = gameOutput.scrollHeight;
    }
}

// ===== INITIALIZE APPLICATION =====
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🌟 Starting Agartha RPG...');
    window.agarthaApp = new AgarthaApp();
    await window.agarthaApp.initialize();
});
